export interface RawModelData {
  id: string;
  name: string;
  thumbnail: string;
  fileType: string;
  size: string;
  createdAt: string;
  filePath?: string;
}

export interface ModelData {
  id: string;
  name: string;
  thumbnail: string;
  fileType: string;
  size: number;
  createdAt: string;
  filePath?: string;
}

export interface MaterialData {
  id: string;
  name: string;
  thumbnail: string;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  createdAt: string;
}

// 使用环境变量或默认值
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3001';

// 处理相对URL，添加服务器基础URL
const ensureFullUrl = (url: string | undefined): string => {
  if (!url) return '';
  return url.startsWith('http') ? url : `${SERVER_URL}${url}`;
};

// 统一捕获和处理API请求错误
const handleApiResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    try {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    } catch {
      // 如果JSON解析失败，直接抛出HTTP错误
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  }
  return response.json();
};

// 转换从API接收的模型数据，确保类型正确
const transformModelData = (model: RawModelData): ModelData => {
  return {
    ...model,
    size: typeof model.size === 'string' ? parseInt(model.size, 10) : model.size || 0,
    thumbnail: ensureFullUrl(model.thumbnail),
    filePath: ensureFullUrl(model.filePath),
  };
};

class ApiService {
  // 模型相关方法
  async getModels(): Promise<ModelData[]> {
    try {
      const response = await fetch(`${BASE_URL}/models`);
      const models = await handleApiResponse<RawModelData[]>(response);
      return models.map(transformModelData);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      return [];
    }
  }

  async addModel(name: string, modelFile: File, thumbnailFile?: File | null, fileType?: string, size?: string): Promise<ModelData | null> {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('modelFile', modelFile);
    if (thumbnailFile) {
      formData.append('thumbnailFile', thumbnailFile);
    }
    if (fileType) {
      formData.append('fileType', fileType);
    }
    if (size) {
      formData.append('size', size);
    }

    try {
      const response = await fetch(`${BASE_URL}/models`, {
        method: 'POST',
        body: formData,
      });
      const newModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(newModel);
    } catch (error) {
      console.error('Failed to add model:', error);
      return null;
    }
  }

  async deleteModel(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, {
        method: 'DELETE',
      });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete model ${id}:`, error);
      return false;
    }
  }

  // 材质相关方法
  async getMaterials(): Promise<MaterialData[]> {
    try {
      const response = await fetch(`${BASE_URL}/materials`);
      const materials = await handleApiResponse<MaterialData[]>(response);
      
      return materials.map((material: MaterialData) => ({
        ...material,
        thumbnail: ensureFullUrl(material.thumbnail),
      }));
    } catch (error) {
      console.error('Failed to fetch materials:', error);
      return [];
    }
  }

  async addMaterial(
    name: string, 
    color: string, 
    metalness: number, 
    roughness: number, 
    glass: number, 
    thumbnailFile?: File | null
  ): Promise<MaterialData | null> {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('color', color);
    formData.append('metalness', metalness.toString());
    formData.append('roughness', roughness.toString());
    formData.append('glass', glass.toString());
    if (thumbnailFile) {
      formData.append('thumbnailFile', thumbnailFile);
    }

    try {
      const response = await fetch(`${BASE_URL}/materials`, {
        method: 'POST',
        body: formData,
      });
      const newMaterial = await handleApiResponse<MaterialData>(response);
      
      return {
        ...newMaterial,
        thumbnail: ensureFullUrl(newMaterial.thumbnail),
      };
    } catch (error) {
      console.error('Failed to add material:', error);
      return null;
    }
  }
  
  // 添加材质更新方法
  async updateMaterial(
    id: string,
    name: string, 
    color: string, 
    metalness: number, 
    roughness: number, 
    glass: number, 
    thumbnailFile?: File | null
  ): Promise<MaterialData | null> {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('color', color);
    formData.append('metalness', metalness.toString());
    formData.append('roughness', roughness.toString());
    formData.append('glass', glass.toString());
    if (thumbnailFile) {
      formData.append('thumbnailFile', thumbnailFile);
    }

    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, {
        method: 'PUT',
        body: formData,
      });
      const updatedMaterial = await handleApiResponse<MaterialData>(response);
      
      return {
        ...updatedMaterial,
        thumbnail: ensureFullUrl(updatedMaterial.thumbnail),
      };
    } catch (error) {
      console.error(`Failed to update material ${id}:`, error);
      return null;
    }
  }

  async deleteMaterial(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, {
        method: 'DELETE',
      });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete material ${id}:`, error);
      return false;
    }
  }
  
  // 获取单个模型详情
  async getModel(id: string): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`);
      const model = await handleApiResponse<RawModelData>(response);
      return transformModelData(model);
    } catch (error) {
      console.error(`Failed to get model ${id}:`, error);
      return null;
    }
  }

  async updateModel(
    id: string,
    name: string,
    thumbnailFile?: File | null
  ): Promise<ModelData | null> {
    const formData = new FormData();
    formData.append('name', name);
    if (thumbnailFile) {
      formData.append('thumbnailFile', thumbnailFile);
    }

    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, {
        method: 'PUT',
        body: formData,
      });
      const updatedModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(updatedModel);
    } catch (error) {
      console.error(`Failed to update model ${id}:`, error);
      return null;
    }
  }
  
  // 获取单个材质详情
  async getMaterial(id: string): Promise<MaterialData | null> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`);
      const material = await handleApiResponse<MaterialData>(response);
      
      return {
        ...material,
        thumbnail: ensureFullUrl(material.thumbnail),
      };
    } catch (error) {
      console.error(`Failed to get material ${id}:`, error);
      return null;
    }
  }
}

// 导出服务实例
export const apiService = new ApiService();