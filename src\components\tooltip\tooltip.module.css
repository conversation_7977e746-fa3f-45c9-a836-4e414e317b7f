.tooltipContainer {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  background-color: var(--color-bg-overlay);
  color: var(--color-content-regular);
  padding: 8px 12px;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  animation: fadeIn 0.2s ease-in-out;
}

.tooltip::before {
  content: '';
  position: absolute;
  border: 6px solid transparent;
}

/* 顶部提示 */
.top::before {
  border-top-color: var(--color-bg-overlay);
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
}

/* 底部提示 */
.bottom::before {
  border-bottom-color: var(--color-bg-overlay);
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
}

/* 左侧提示 */
.left::before {
  border-left-color: var(--color-bg-overlay);
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
}

/* 右侧提示 */
.right::before {
  border-right-color: var(--color-bg-overlay);
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
}
