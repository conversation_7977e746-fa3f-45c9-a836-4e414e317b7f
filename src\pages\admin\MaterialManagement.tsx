import { useState, useEffect } from 'react';
import { SearchBox } from '../../components/search-box/search-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import { SecondaryButton } from '../../components/secondary-button/secondary-button';
import { IconButton } from '../../components/icon-button/icon-button';
import { CustomMaterialPanel } from '../../components/custom-material-panel/custom-material-panel';
import { Loading } from '../../components/loading/loading';
import type { MaterialSettings } from '../../components/custom-material-panel/custom-material-panel';
import { Trash, Edit, Palette } from 'lucide-react';
import MaterialPreview from '../../components/material-preview/MaterialPreview';
import './AdminTable.css';
import '../../components/modal/modal.css';

import { apiService } from '../../services/api';
import type { MaterialData } from '../../services/api';
import MaterialThumbnailSimple from '../../components/material-thumbnail/MaterialThumbnailSimple';


const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const MaterialManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<MaterialData | null>(null);
  const [loading, setLoading] = useState(false);
  const [materials, setMaterials] = useState<MaterialData[]>([]);

  // 加载材质列表
  useEffect(() => {
    const loadMaterials = async () => {
      setLoading(true);
      try {
        const data = await apiService.getMaterials();
        setMaterials(data);
      } catch (error) {
        console.error('加载材质失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMaterials();
  }, []);

  const deleteMaterial = async (id: string) => {
    if (window.confirm('确定要删除这个材质吗？')) {
      try {
        await apiService.deleteMaterial(id);
        setMaterials(materials.filter(material => material.id !== id));
      } catch (error) {
        console.error('删除材质失败:', error);
      }
    }
  };

  const filteredMaterials = materials.filter(material => 
    material.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return <Loading text="正在加载材质列表..." variant="minimal" />;
  }

  return (
    <div className="material-management">
      <div className="management-toolbar">
        <SearchBox 
          placeholder="搜索材质" 
          value={searchQuery} 
          onChange={setSearchQuery}
          width={300}
        />
        
        <div className="toolbar-actions">
          <PrimaryButton onClick={() => setShowAddModal(true)}>
            添加材质
          </PrimaryButton>
        </div>
      </div>
      
      <div className="admin-table-container">
        <table className="admin-table">
          <thead>
            <tr>
              <th>缩略图</th>
              <th>材质名称</th>
              <th>颜色</th>
              <th>金属感</th>
              <th>粗糙度</th>
              <th>不透明度</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredMaterials.map(material => (
              <tr key={material.id} className="material-row">
                <td className="thumbnail-cell">
                  <MaterialThumbnailSimple 
                    material={material} 
                    size={44}
                  />
                </td>
                <td className="material-name">{material.name}</td>
                <td className="material-color">
                  <div 
                    className="color-preview" 
                    style={{ backgroundColor: material.color }}
                    title={material.color}
                  ></div>
                </td>
                <td>{material.metalness}%</td>
                <td>{material.roughness}%</td>
                <td>{100 - material.glass}%</td>
                <td className="date-cell">{formatDate(material.createdAt)}</td>
                <td className="actions-cell">
                  <IconButton icon={Edit} size="small" onClick={() => setSelectedMaterial(material)} />
                  <IconButton icon={Trash} size="small" onClick={() => deleteMaterial(material.id)} />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showAddModal && (
        <MaterialModal 
          onClose={() => setShowAddModal(false)} 
          onSave={async (data) => {
            try {
              const { name, color, metalness, roughness, glass, thumbnailFile } = data;
              if (name && color && metalness !== undefined && roughness !== undefined && glass !== undefined) {
                const newMaterial = await apiService.addMaterial(
                  name,
                  color,
                  metalness,
                  roughness,
                  glass,
                  thumbnailFile
                );
                if (newMaterial) {
                  setMaterials([...materials, newMaterial]);
                } else {
                  console.error('添加材质失败: 未返回新材质数据');
                  alert('添加材质失败，请检查控制台获取更多信息。');
                }
              } else {
                console.error('添加材质失败: 缺少必要的材质信息', data);
                alert('添加材质失败: 缺少必要的材质信息。');
              }
              setShowAddModal(false);
            } catch (error) {
              console.error('添加材质失败:', error);
              alert(`添加材质失败: ${error instanceof Error ? error.message : '未知错误'}`);
            }
          }}
        />
      )}
      
      {selectedMaterial && (
        <MaterialModal 
          material={selectedMaterial}
          onClose={() => setSelectedMaterial(null)} 
          onSave={async () => {
            // 材质更新功能暂未实现
            alert('更新材质功能暂未实现');
            setSelectedMaterial(null);
          }}
        />
      )}
    </div>
  );
};

interface MaterialModalProps {
  material?: MaterialData;
  onClose: () => void;
  onSave: (data: Partial<Omit<MaterialData, 'id' | 'createdAt' | 'thumbnail'>> & { thumbnailFile?: File | null }) => void | Promise<void>;
}

// 材质添加/编辑弹窗
const MaterialModal = ({ material, onClose, onSave }: MaterialModalProps) => {
  const [name, setName] = useState(material?.name || '');
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [materialSettings, setMaterialSettings] = useState<MaterialSettings>({
    color: material?.color || '#B39B9C',
    metalness: material ? material.metalness / 100 : 0.5,
    roughness: material ? material.roughness / 100 : 0.5,
    opacity: material && typeof material.glass === 'number' ? (100 - material.glass) / 100 : 1,
  });
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ 
      name,
      color: materialSettings.color,
      metalness: Math.round(materialSettings.metalness * 100),
      roughness: Math.round(materialSettings.roughness * 100),
      glass: Math.round((1 - materialSettings.opacity) * 100),
      thumbnailFile
    });
  };
  
  return (
    <div className="modal-overlay">
      <div className="modal-container material-modal-container">
        <div className="modal-header">
          <h2 className="modal-title">{material ? '编辑材质' : '添加新材质'}</h2>
          <SecondaryButton onClick={onClose}>关闭</SecondaryButton>
        </div>
        <div className="modal-content">
          <form className="modal-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label>材质名称</label>
            <input 
              type="text" 
              value={name} 
              onChange={(e) => setName(e.target.value)}
              placeholder="输入材质名称"
              required
              className="form-input"
            />
          </div>
          
          <div className="material-preview">
            <div className="preview-header">
              <Palette size={18} />
              <span>材质预览</span>
            </div>
            <div className="preview-sphere">
              <MaterialPreview settings={materialSettings} size={120} />
            </div>
          </div>
          
          <div className="material-settings">
            <CustomMaterialPanel 
              defaultSettings={materialSettings}
              onChange={(settings) => {
                setMaterialSettings(settings as MaterialSettings);
              }}
            />
          </div>

          <div className="form-group">
            <label>上传缩略图 (可选)</label>
            <div className="file-upload-area">
              <input 
                type="file" 
                id="material-thumbnail-input"
                onChange={(e) => setThumbnailFile(e.target.files?.[0] || null)} 
                accept="image/png, image/jpeg, image/webp"
                className="file-input"
              />
              <SecondaryButton 
                onClick={() => document.getElementById('material-thumbnail-input')?.click()}
                type="button"
              >
                {thumbnailFile ? thumbnailFile.name : material?.thumbnail ? '更换缩略图' : '选择缩略图'}
              </SecondaryButton>
              <span className="file-info">
                支持 .png, .jpg, .webp 格式
              </span>
            </div>
          </div>
          
          <div className="modal-actions">
            <SecondaryButton onClick={onClose} type="button">
              取消
            </SecondaryButton>
            <PrimaryButton type="submit">
              {material ? '保存修改' : '添加材质'}
            </PrimaryButton>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default MaterialManagement;