/* Shared styles for admin management modals */

/* --- Animations --- */
@keyframes modal-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* --- Modal Overlay and Container --- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(20, 20, 22, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modal-fade-in 0.2s ease-out;
}

.modal-container {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 480px;
  box-shadow: var(--shadow-modal);
  border: 1px solid var(--color-border);
  max-height: 90vh;
  overflow-y: auto;
  animation: modal-slide-in 0.3s ease-out;
}

/* --- Modal Header --- */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 500;
  margin: 0;
  color: var(--color-content-accent);
}

/* --- Modal Content --- */
.modal-content {
  padding: 24px;
}

/* --- Modal Form --- */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  background-color: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-brand);
  box-shadow: 0 0 0 2px var(--color-brand-hover);
}

/* --- Modal Actions --- */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
}

/* --- File Upload --- */
.file-upload-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-input {
  display: none;
}

.file-info {
  font-size: var(--font-size-xs);
  color: var(--color-content-mute);
}


/* --- Model-specific styles --- */
.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-base);
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  color: var(--color-content-secondary);
  background-color: var(--color-bg-hover);
}

.upload-area.drag-over,
.upload-area:hover {
  background-color: var(--color-bg-hover);
  border-color: var(--color-brand);
}

.upload-area p {
  margin: 8px 0 0;
  font-size: var(--font-size-sm);
}

.upload-area .file-name {
  color: var(--color-brand);
  font-weight: 500;
}


/* --- Material-specific styles --- */
.material-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  font-weight: 500;
}

.preview-sphere {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px; /* Ensure space for the preview */
}